import {
  Text,
  Field,
  withData<PERSON>ur<PERSON><PERSON>he<PERSON>,
  RichText,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import axios from 'axios';
import { useEffect, useMemo, useState } from 'react';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { GetConnectDate } from 'src/services/CalendarAPI/types';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import CalendarValidator from 'src/utils/calendarValidator';
import Calendar from 'components/common/Calendar/Calendar';
import { UseFormReturnType } from '@mantine/form';
import { TransferOrderInfoFormType } from '../TransferOrderInfoContainer/TransferOrderInfoContainer';
import { faChevronDown, faChevronUp } from '@fortawesome/pro-light-svg-icons';
import dayjs from 'dayjs';
import { Loader } from '@mantine/core';
import timezone from 'dayjs/plugin/timezone';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

type TransferSetServiceDateProps = ComponentProps & {
  fields: {
    Header: Field<string>;
    Description: Field<string>;
    OldAddress: Field<string>;
    OldAddressTypeaHeadLabel: Field<string>;
    TipsHeader: Field<string>;
    TipsDescription: Field<string>;
    NewAddress: Field<string>;
    NewAddressTypeaHeadLabel: Field<string>;
    PriorityConnect: Field<string>;
    ConnectDateDisclaimer: Field<string>;
    ConnectDatePriorityDisclaimer: Field<string>;
    ConnectDateHolidays: Field<string>;
    ConnectCalendarDays: Field<string>;
    DisConnectDateDisclaimer: Field<string>;
    DisConnectDatePriorityDisclaimer: Field<string>;
    DisConnectDateHolidays: Field<string>;
    DisConnectCalendarDays: Field<string>;
  };
  form: UseFormReturnType<TransferOrderInfoFormType>;
};

const TransferSetServiceDate = (props: TransferSetServiceDateProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  const [showList, setShowList] = useState(false);
  let personalInfo = undefined;
  let dispatch: ReturnType<typeof useAppDispatch>;
  if (!isPageEditing) {
    personalInfo = useAppSelector((state) => state.transfer?.personalInfo);
    dispatch = useAppDispatch();
  }
  const [calendarData, setCalendarData] = useState<GetConnectDate | null>(null);
  const [disConnectedCalendarData, setDisConnectedCalendarData] = useState<GetConnectDate | null>(
    null
  );

  dayjs.extend(timezone);
  useEffect(() => {
    //Convert Date into Central Standard Time
    const CSTTimeNow = dayjs.tz(new Date(), 'America/Chicago');
    const currentMonth = CSTTimeNow.month();
    const currentDate = CSTTimeNow.date();
    const currentYear = CSTTimeNow.year();
    const fetchConnectDate = async () => {
      if (personalInfo?.newServiceAddress.esiid) {
        try {
          const req = await axios.get<GetConnectDate>(
            `/api/calendar/transferconnectdate?esiid=${personalInfo.newServiceAddress.esiid}`
          );
          setCalendarData(req.data);
        } catch (err: unknown) {
          //  const error = err as AxiosError;
        }
      }
    };

    const fetchDisConnectDate = async () => {
      if (personalInfo?.oldEsiid) {
        try {
          const req = await axios.get<GetConnectDate>(
            `/api/calendar/transferdisconnectdate?esiid=${personalInfo.oldEsiid}`
          );
          if (req.data.result.standardDays.length > 0) {
            if (new Date(req.data.result.standardDays[0]).getDate() === CSTTimeNow.date()) {
              req.data.result.standardDays[0] = new Date(
                currentYear,
                currentMonth,
                currentDate + 1
              ).toDateString();
            }
          } else {
            req.data.result.standardDays.push(
              new Date(currentYear, currentMonth, currentDate + 1).toDateString()
            );
          }
          setDisConnectedCalendarData(req.data);
        } catch (err: unknown) {
          //  const error = err as AxiosError;
        }
      }
    };

    fetchConnectDate();
    fetchDisConnectDate();
  }, [personalInfo?.newServiceAddress.esiid, personalInfo?.oldEsiid]);

  const connectCalendarValidator = useMemo(() => {
    if (calendarData) {
      const data = calendarData?.result;
      const newValidator = new CalendarValidator(
        data?.workDays,
        data?.holidays.concat(props.fields.ConnectDateHolidays.value.split(',')),
        data?.priorityDays,
        data?.standardDays
      );
      return newValidator;
    } else return null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calendarData, dispatch]);

  const disConnectCalendarValidator = useMemo(() => {
    if (disConnectedCalendarData) {
      const data = disConnectedCalendarData?.result;
      const newValidator = new CalendarValidator(
        data?.workDays,
        data?.holidays.concat(props.fields.DisConnectDateHolidays.value.split(',')),
        data?.priorityDays,
        data?.standardDays
      );
      return newValidator;
    } else return null;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [disConnectedCalendarData, dispatch]);

  return (
    <div className="flex flex-col w-full max-w-[832px] sm:px-0 px-6 my-8 mb-0 ipad:pl-[20px] wide:pl-[20px]">
      <div className="text-plus2 sm:text-plus2 text-textQuattuordenary font-primaryBold mb-4 sm:mb-[10px]">
        {props.fields.Header.value}
      </div>

      <RichText className="text-base leading-[30px]" field={props.fields.Description} tag="p" />

      <div>
        <div className="mt-[20px]">
          <Text
            tag="p"
            className="text-base leading-[30px] font-primaryBold  text-textQuattuordenary"
            field={props.fields.OldAddress}
          />
          <p className="text-minus2">{personalInfo?.oldServiceAddress}</p>
        </div>
        <div className="mt-[20px]">
          <Text
            tag="p"
            className="text-base leading-[30px] text-textQuattuordenary pb-2"
            field={props.fields.OldAddressTypeaHeadLabel}
          />
          {disConnectedCalendarData && disConnectCalendarValidator ? (
            <Calendar<TransferOrderInfoFormType>
              form={props.form}
              formField={'stopServiceDate'}
              calendarData={disConnectedCalendarData}
              calendarDesclaimer={props.fields.DisConnectDateDisclaimer.value}
              calendarPriorityDisclaimer={props.fields.DisConnectDatePriorityDisclaimer.value}
              calendarValidator={disConnectCalendarValidator}
              calendarDays={parseInt(props.fields.DisConnectCalendarDays.value)}
              error={undefined}
            />
          ) : (
            <Loader size="sm" />
          )}
        </div>
        <div
          className="mt-[15px] cursor-pointer flex flex-row w-fit h-fit items-center decoration-solid decoration-textTertiary decoration-2"
          onClick={() => setShowList(!showList)}
        >
          <Text
            tag="p"
            className="text-minus1 text-text-minus1 text-textQuattuordenary"
            field={props.fields.TipsHeader}
          />
          {showList ? (
            <FontAwesomeIcon
              icon={faChevronUp}
              className="text-textPrimary hover:text-textPrimary"
            />
          ) : (
            <FontAwesomeIcon
              icon={faChevronDown}
              className="text-textPrimary hover:text-textPrimary"
            />
          )}
        </div>
        <div
          className={`${showList
              ? 'text-textQuattuordenary text-minus1 font-primaryRegular  tracking-wide leading-[26px]'
              : 'hidden'
            }`}
        >
          <RichText className="" field={props.fields.TipsDescription} tag="p" />
        </div>
      </div>
      <div className="mt-[40px]">
        <div>
          <Text
            tag="p"
            className="text-base leading-[30px] font-primaryBold text-textQuattuordenary"
            field={props.fields.NewAddress}
          />
          <p className="text-minus2">{personalInfo?.newServiceAddress.display_text}</p>
        </div>
        <div className="my-6">
          <Text
            tag="p"
            className="text-base leading-[30px] text-textQuattuordenary pb-2"
            field={props.fields.NewAddressTypeaHeadLabel}
          />
          {calendarData && connectCalendarValidator ? (
            <Calendar<TransferOrderInfoFormType>
              form={props.form}
              formField={'startServiceDate'}
              calendarData={calendarData}
              calendarDesclaimer={props.fields.ConnectDateDisclaimer.value}
              calendarPriorityDisclaimer={props.fields.ConnectDatePriorityDisclaimer.value}
              calendarValidator={connectCalendarValidator}
              calendarDays={parseInt(props.fields.ConnectCalendarDays.value)}
              error={undefined}
            />
          ) : (
            <Loader size="sm" />
          )}
          {props.form.values.startServiceDate &&
            calendarData &&
            connectCalendarValidator &&
            connectCalendarValidator.isPriorityDay(
              dayjs(props.form.values.startServiceDate).toDate()
            ) ? (
            <Text
              tag="p"
              className="text-textDenary mt-3"
              field={{
                value: props.fields.PriorityConnect.value
                  .replace('${date}', props.form.values.startServiceDate)
                  .replace('${priorityfee}', `$${calendarData.result.priorityFee.toString()}`),
              }}
            />
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
};

export { TransferSetServiceDate };
const Component = withDatasourceCheck()(TransferSetServiceDate);
export default aiLogger(Component, Component.name);
