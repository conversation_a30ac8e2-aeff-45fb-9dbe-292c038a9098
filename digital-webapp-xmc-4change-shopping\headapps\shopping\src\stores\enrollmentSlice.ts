import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { PaymentResultCollection, PriorDebtData } from 'src/services/EnrollmentAPI/types';
import { KBAQuestion } from 'src/services/OOWAPI/types';

interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  dateOfBirth: string | null;
  phoneNumber: number | undefined;
  isMobile: boolean;
  correspondanceLanguage: string;
  emailCopiesList: { email: string }[];
  mobileNumber?: string | null;
  AmbitReferralId: string;
}

interface IdentityInfo {
  socialSecurityNumber: string;
  driverLicenseNumber: string;
  driverLicenseState: string | null;
}

interface ServiceInfo {
  esiid: string;
  poBox: string | null;
  houseNbr: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  poBoxCity: string;
  poBoxState: string;
  poBoxZipCode: string;
  unit: string;
  tdsp: string;
  startDate?: string | null;
}

interface EnrollmentInfo {
  bpNumber: string;
  contractAccountNumber: string;
  serviceAccountNumber: string;
}

interface PriorDebtInfo {
  ThresholdPassed: boolean;
  PriorDebt: PriorDebtData[];
}

interface PaymentInfo {
  paymentType: string;
  priorDebtPaid: boolean;
  paymentMethod: string;
  cardHolderName: string;
  cardNumber: string;
  expirationDate: string;
  zipCode: string;
  scheduledDepositDate: string;
}

interface BillingInfo {
  isSameAddress: boolean;
  billingStreetNumber: string;
  billingStreetAddress: string;
  billingAptOrUnit: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  secondaryAccountFirstName: string;
  secondaryAccountLastName: string;
}

interface MyAccountInfo {
  access_token: string;
  expires_in: number;
}

interface OOWInfo {
  sessionID: string;
  clientReferenceId: string;
  score: string;
  decision: string;
  kba: KBAQuestion[];
}

interface OOWDataLayer {
  OOW_OTP_Sent: boolean;
  OOW_OTP_Response: string;
  OOW_CustomerType: string;
  OOW_KIQ_Sent: boolean;
  OOW_KIQ_Reponse: string;
}
interface SolarInfo {
  solarConsultantEmail: string;
  ownHome: boolean;
  homeType: string;
}

interface CharityInfo {
  id: string;
  code: string;
  name: string;
  image: string;
}

type PriorDebtSuccessPaymentInfo = Record<string, PaymentResultCollection>;

export interface EnrollmentState {
  customerInfo: CustomerInfo;
  identityInfo: IdentityInfo;
  serviceInfo: ServiceInfo;
  startDate: string;
  channel: string;
  vendorId: string;
  enrollmentInfo: EnrollmentInfo;
  priorDebtInfo: PriorDebtInfo;
  PriorDebtSuccessPaymentInfo: PriorDebtSuccessPaymentInfo;
  depositAmount: number;
  isDepositRequired: boolean;
  autopayEligible: boolean;
  paymentInfo: PaymentInfo;
  isPaperlessBilling: boolean;
  isAutoPay: boolean;
  billingInfo: BillingInfo;
  isCoaSuccess: boolean;
  userName: string;
  showMyAccountBtn: boolean;
  myAccountInfo: MyAccountInfo;
  correlationid: string;
  sessionid: string;
  oowInfo: OOWInfo;
  oowDataLayerInfo: OOWDataLayer;
  isRedirectOops: boolean;
  DRSActionToken: string;
  errorMessage: string;
  errorCode: string;
  DRSActionId: string;
  isSunRunProductEligible: boolean;
  solarInfo: SolarInfo;
  charityInfo: CharityInfo;
  isRenewableEnergy: boolean;
  isAutoPayFailure: boolean;
  languageOptionalValue: string;
}

const initialState: EnrollmentState = {
  customerInfo: {
    firstName: '',
    lastName: '',
    email: '',
    dateOfBirth: '',
    phoneNumber: 0,
    isMobile: false,
    correspondanceLanguage: '',
    emailCopiesList: [],
    AmbitReferralId: '',
  },
  identityInfo: {
    socialSecurityNumber: '',
    driverLicenseNumber: '',
    driverLicenseState: null,
  },
  serviceInfo: {
    esiid: '',
    poBox: null,
    houseNbr: '',
    street: '',
    city: '',
    state: '',
    postalCode: '',
    poBoxCity: '',
    poBoxState: '',
    poBoxZipCode: '',
    unit: '',
    tdsp: '',
    startDate: '',
  },
  startDate: '',
  isPaperlessBilling: false,
  isAutoPay: false,
  isCoaSuccess: false,
  isRedirectOops: true,
  userName: '',
  showMyAccountBtn: true,
  channel: 'web',
  vendorId: '',
  enrollmentInfo: {
    bpNumber: '',
    contractAccountNumber: '',
    serviceAccountNumber: '',
  },
  priorDebtInfo: {
    ThresholdPassed: false,
    PriorDebt: [],
  },
  PriorDebtSuccessPaymentInfo: {},
  depositAmount: 0,
  isDepositRequired: false,
  autopayEligible: false,
  paymentInfo: {
    paymentType: '',
    priorDebtPaid: false,
    paymentMethod: '',
    cardHolderName: '',
    cardNumber: '',
    expirationDate: '',
    zipCode: '',
    scheduledDepositDate: '',
  },
  billingInfo: {
    isSameAddress: false,
    billingStreetNumber: '',
    billingStreetAddress: '',
    billingAptOrUnit: '',
    billingCity: '',
    billingState: '',
    billingZipCode: '',
    secondaryAccountFirstName: '',
    secondaryAccountLastName: '',
  },
  myAccountInfo: {
    access_token: '',
    expires_in: 0,
  },
  oowInfo: {
    sessionID: '',
    clientReferenceId: '',
    score: '',
    decision: '',
    kba: [],
  },
  oowDataLayerInfo: {
    OOW_OTP_Sent: false,
    OOW_OTP_Response: '',
    OOW_CustomerType: '',
    OOW_KIQ_Sent: false,
    OOW_KIQ_Reponse: '',
  },
  correlationid: '',
  sessionid: '',
  errorMessage: '',
  errorCode: '',
  DRSActionId: '',
  DRSActionToken: '',
  isSunRunProductEligible: false,
  solarInfo: {
    solarConsultantEmail: '',
    ownHome: false,
    homeType: '',
  },
  charityInfo: {
    id: '',
    code: '',
    name: '',
    image: '',
  },
  isRenewableEnergy: false,
  isAutoPayFailure: false,
  languageOptionalValue: '',
};

export const enrollmentSlice = createSlice({
  name: 'enrollment',
  initialState,
  reducers: {
    setCustomerInfo: (state, action: PayloadAction<CustomerInfo>) => {
      state.customerInfo = action.payload;
    },
    setIdentityInfo: (state, action: PayloadAction<IdentityInfo>) => {
      state.identityInfo = action.payload;
    },
    setServiceInfo: (state, action: PayloadAction<ServiceInfo>) => {
      state.serviceInfo = action.payload;
    },
    setVendorId: (state, action: PayloadAction<string>) => {
      state.vendorId = action.payload;
    },
    setStartDate: (state, action: PayloadAction<string>) => {
      state.startDate = action.payload;
    },
    setPaperLessBilling: (state, action: PayloadAction<boolean>) => {
      state.isPaperlessBilling = action.payload;
    },
    setAutoPay: (state, action: PayloadAction<boolean>) => {
      state.isAutoPay = action.payload;
    },
    setMyAccountInfo: (state, action: PayloadAction<MyAccountInfo>) => {
      state.myAccountInfo = action.payload;
    },
    setCoaSuccess: (state, action: PayloadAction<boolean>) => {
      state.isCoaSuccess = action.payload;
    },
    setOopsRedirect: (state, action: PayloadAction<boolean>) => {
      state.isRedirectOops = action.payload;
    },
    setShowMyAccountBtn: (state, action: PayloadAction<boolean>) => {
      state.showMyAccountBtn = action.payload;
    },
    setEnrollmentInfo: (state, action: PayloadAction<EnrollmentInfo>) => {
      state.enrollmentInfo = action.payload;
    },
    setServiceAccountNumber: (state, action: PayloadAction<string>) => {
      state.enrollmentInfo.serviceAccountNumber = action.payload;
    },
    setPriorDebtInfo: (state, action: PayloadAction<PriorDebtInfo>) => {
      state.priorDebtInfo = action.payload;
    },
    setDepositAmount: (state, action: PayloadAction<number>) => {
      state.depositAmount = action.payload;
    },
    setIsDepositRequired: (state, action: PayloadAction<boolean>) => {
      state.isDepositRequired = action.payload;
    },
    setAutopayEligible: (state, action: PayloadAction<boolean>) => {
      state.autopayEligible = action.payload;
    },
    setPaymentInfo: (state, action: PayloadAction<PaymentInfo>) => {
      state.paymentInfo = action.payload;
    },
    setScheduledDepositDate: (state, action: PayloadAction<string>) => {
      state.paymentInfo = { ...state.paymentInfo, scheduledDepositDate: action.payload };
    },
    setBillingInfo: (state, action: PayloadAction<BillingInfo>) => {
      state.billingInfo = action.payload;
    },
    setPriorDebtSuccessPaymentInfo: (state, action: PayloadAction<PaymentResultCollection[]>) => {
      const successPayments: PriorDebtSuccessPaymentInfo = {};
      action.payload.forEach((priorDebtData) => {
        successPayments[priorDebtData.priorDebtDetail.ContractAccountNumber] = priorDebtData;
      });
      const newState: EnrollmentState = {
        ...state,
        PriorDebtSuccessPaymentInfo: {
          ...state.PriorDebtSuccessPaymentInfo,
          ...successPayments,
        },
      };
      return newState;
    },
    setCorrelationId: (state, action: PayloadAction<string>) => {
      state.correlationid = action.payload;
    },
    setSessionId: (state, action: PayloadAction<string>) => {
      state.sessionid = action.payload;
    },
    setOOWInfo: (state, action: PayloadAction<OOWInfo>) => {
      state.oowInfo = action.payload;
    },
    clearOOW: (state) => {
      state.oowInfo = { sessionID: '', clientReferenceId: '', score: '', decision: '', kba: [] };
    },
    clearEnrollment: (state) => {
      const newState: EnrollmentState = {
        ...initialState,
        correlationid: state.correlationid,
      };
      return newState;
    },
    setCustomerUserName: (state, action: PayloadAction<string>) => {
      state.userName = action.payload;
    },
    setOOWDLInfo: (state, action: PayloadAction<OOWDataLayer>) => {
      state.oowDataLayerInfo = action.payload;
    },
    setDRSToken: (state, action: PayloadAction<string>) => {
      state.DRSActionToken = action.payload;
    },
    setErrorMessage: (state, action: PayloadAction<string>) => {
      state.errorMessage = action.payload;
    },
    setErrorCode: (state, action: PayloadAction<string>) => {
      state.errorCode = action.payload;
    },
    setDRSActionId: (state, action: PayloadAction<string>) => {
      state.DRSActionId = action.payload;
    },
    setIsSunRunProductEligible: (state, action: PayloadAction<boolean>) => {
      state.isSunRunProductEligible = action.payload;
    },
    setSolarInfo: (state, action: PayloadAction<SolarInfo>) => {
      state.solarInfo = action.payload;
    },
    setSelectedCharityInfo: (state, action: PayloadAction<CharityInfo>) => {
      state.charityInfo = action.payload;
    },
    setRenewableEnergy: (state, action: PayloadAction<boolean>) => {
      state.isRenewableEnergy = action.payload;
    },
    setAutoPayFailure: (state, action: PayloadAction<boolean>) => {
      state.isAutoPayFailure = action.payload;
    },
    setLanguageOptionalValue: (state, action: PayloadAction<string>) => {
      state.languageOptionalValue = action.payload;
    },
  },
});

export const {
  setCustomerInfo,
  setIdentityInfo,
  setServiceInfo,
  setStartDate,
  setVendorId,
  setEnrollmentInfo,
  setServiceAccountNumber,
  setPriorDebtInfo,
  setDepositAmount,
  setPaymentInfo,
  setScheduledDepositDate,
  setPaperLessBilling,
  setBillingInfo,
  setPriorDebtSuccessPaymentInfo,
  setCoaSuccess,
  setOopsRedirect,
  setShowMyAccountBtn,
  setIsDepositRequired,
  setAutopayEligible,
  setMyAccountInfo,
  setCorrelationId,
  setSessionId,
  clearEnrollment,
  setAutoPay,
  setOOWInfo,
  clearOOW,
  setCustomerUserName,
  setOOWDLInfo,
  setDRSToken,
  setErrorMessage,
  setErrorCode,
  setDRSActionId,
  setIsSunRunProductEligible,
  setSolarInfo,
  setSelectedCharityInfo,
  setRenewableEnergy,
  setAutoPayFailure,
  setLanguageOptionalValue,
} = enrollmentSlice.actions;

export default enrollmentSlice.reducer;
